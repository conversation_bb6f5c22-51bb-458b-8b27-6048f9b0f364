<?php return array(
    'root' => array(
        'name' => 'yiisoft/yii2-app-advanced',
        'pretty_version' => '2.0.53',
        'version' => '2.0.53.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'behat/gherkin' => array(
            'pretty_version' => 'v4.14.0',
            'version' => '4.14.0.0',
            'reference' => '34c9b59c59355a7b4c53b9f041c8dbd1c8acc3b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../behat/gherkin',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'bower-asset/bootstrap' => array(
            'pretty_version' => 'v5.3.7',
            'version' => '5.3.7.0',
            'reference' => 'e0032ae6a5a628a51a8552091816cec09b6434df',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/bootstrap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/inputmask' => array(
            'pretty_version' => '5.0.9',
            'version' => '5.0.9.0',
            'reference' => '310a33557e2944daf86d5946a5e8c82b9118f8f7',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/inputmask',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/jquery' => array(
            'pretty_version' => '3.7.1',
            'version' => '3.7.1.0',
            'reference' => 'fde1f76e2799dd877c176abde0ec836553246991',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/jquery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/punycode' => array(
            'pretty_version' => 'v1.4.1',
            'version' => '1.4.1.0',
            'reference' => '0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/punycode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/yii2-pjax' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'a9298d57da63d14a950f1b94366a864bc62264fb',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/yii2-pjax',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cebe/markdown' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => '9bac5e971dd391e2802dca5400bbeacbaea9eb86',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cebe/markdown',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'codeception/codeception' => array(
            'pretty_version' => '5.3.2',
            'version' => '5.3.2.0',
            'reference' => '582112d7a603d575e41638df1e96900b10ae91b8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/codeception',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/lib-asserts' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '06750a60af3ebc66faab4313981accec1be4eefc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/lib-asserts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/lib-innerbrowser' => array(
            'pretty_version' => '3.1.3',
            'version' => '3.1.3.0',
            'reference' => '10482f7e34c0537bf5b87bc82a3d65a1842a8b4f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/lib-innerbrowser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/lib-web' => array(
            'pretty_version' => '1.0.7',
            'version' => '1.0.7.0',
            'reference' => '1444ccc9b1d6721f3ced8703c8f4a9041b80df93',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/lib-web',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/module-asserts' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => 'eb1f7c980423888f3def5116635754ae4a75bd47',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/module-asserts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/module-filesystem' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '0fd78cf941cb72dc2a650c6132c5999c26ad4f9a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/module-filesystem',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/module-yii2' => array(
            'pretty_version' => '1.1.12',
            'version' => '1.1.12.0',
            'reference' => '1ebe6bc2a7f307a6c246026a905612a40ef64859',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/module-yii2',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/phpunit-wrapper' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'codeception/stub' => array(
            'pretty_version' => '4.1.4',
            'version' => '4.1.4.0',
            'reference' => '6ce453073a0c220b254dd7f4383645615e4071c3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/stub',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'codeception/verify' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '25b84a96f0fe7dcf28e8021f02b57643b751a707',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeception/verify',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'c6222283fa3f4ac679f8b9ced9a4e23f163e80d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.24.1',
            'version' => '1.24.1.0',
            'reference' => 'e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.3',
            'version' => '1.13.3.0',
            'reference' => 'faed855a7b5f4d4637717c2b3863e277116beb36',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'ae59794362fe85e051a58ad36b289443f57be7a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpspec/php-diff' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => 'fc1156187f9f6c8395886fe85ed88a0a245d72e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpspec/php-diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '9.2.32',
            'version' => '9.2.32.0',
            'reference' => '85402a822d1ecf1db1096959413d35e1c37cf1a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '3.0.6',
            'version' => '3.0.6.0',
            'reference' => 'cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '3.1.1',
            'version' => '3.1.1.0',
            'reference' => '5a10147d0aaf65b58940a0b72f71c9ac0423cc67',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => '5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '9.5.28',
            'version' => '9.5.28.0',
            'reference' => '954ca3113a03bf780d22f07bf055d883ee04b65e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.9',
            'version' => '0.12.9.0',
            'reference' => '1b801844becfe648985372cb4b12ad6840245ace',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '2b56bea83a09de3ac06bb18b92f068e60cc6f50b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '1.0.8',
            'version' => '1.0.8.0',
            'reference' => '1fc9f64c0927627ef78ba436c9b17d967e68e120',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '4.0.8',
            'version' => '4.0.8.0',
            'reference' => 'fa0f136dd2334583309d32b62544682ee972b51a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '25f207c40d62b8b7aa32f5ab026c53561964053a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '5.1.5',
            'version' => '5.1.5.0',
            'reference' => '830c43a844f1f8d5b7a1f6d6076b784454d8b7ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => '78c00df8f170e02473b682df15bfcdacc3d32d72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '5.0.7',
            'version' => '5.0.7.0',
            'reference' => 'bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '1.0.4',
            'version' => '1.0.4.0',
            'reference' => 'e1e4a170560925c26d424b6a03aed157e7dcc5c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => '5c9eeac41b290a3712d88851518825ad78f45c71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b4f479ebdbf63ac605d183ece17d8d7fe49c15c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => 'e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/resource-operations' => array(
            'pretty_version' => '3.0.4',
            'version' => '3.0.4.0',
            'reference' => '05d5692a7993ecccd56a03e40cd7e5b09b1d404e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/resource-operations',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'c6c1022351a901512170118436c764e473f6de8c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/browser-kit' => array(
            'pretty_version' => 'v6.4.19',
            'version' => '6.4.19.0',
            'reference' => 'ce95f3e3239159e7fa3be7690c6ce95a4714637f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/browser-kit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '9e27aecde8f506ba0fd1d9989620c04a87697101',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '601a5ce9aaad7bf10797e3663faefce9e26c24e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dom-crawler' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '22210aacb35dbadd772325d759d17bce2374a84d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dom-crawler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '497f73ac996a598c92409b44ac43b6690c4f666d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '59eb412e93815df44f05f342958efa9f46b1e586',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => 'b5db5105b290bdbea5ab27b89c69effcf1cb3368',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'f3570b8c61ca887a9e2938e85cb6458515d2b125',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '6e209fbe5f5a7b6043baba46fe5735a4b85d0d42',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '0c3555045a46ab3cd4cc5a69d161225195230edb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'yiisoft/yii2' => array(
            'pretty_version' => '2.0.53',
            'version' => '2.0.53.0',
            'reference' => '6c622fb8243181d7912b62ad80821cc0e1c745db',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yiisoft/yii2',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-app-advanced' => array(
            'pretty_version' => '2.0.53',
            'version' => '2.0.53.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-bootstrap5' => array(
            'pretty_version' => '2.0.50',
            'version' => '2.0.50.0',
            'reference' => 'ad080a7ea063074888c2d801f6b05162064f8ae0',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-bootstrap5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-composer' => array(
            'pretty_version' => '2.0.11',
            'version' => '2.0.11.0',
            'reference' => 'b684b01ecb119c8287721def726a0e24fec2fef2',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../yiisoft/yii2-composer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-debug' => array(
            'pretty_version' => '2.1.27',
            'version' => '2.1.27.0',
            'reference' => '44e158914911ef81cd7111fd6d46b918f65fae7c',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-debug',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'yiisoft/yii2-faker' => array(
            'pretty_version' => '2.0.5',
            'version' => '2.0.5.0',
            'reference' => '8c361657143bfaea58ff7dcc9bf51f1991a46f5d',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-faker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'yiisoft/yii2-gii' => array(
            'pretty_version' => '2.2.7',
            'version' => '2.2.7.0',
            'reference' => 'f17c7ef7ef3081213f612b37ad0ceaa8e8cd3d3b',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-gii',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'yiisoft/yii2-symfonymailer' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '82f5902551a160633c4734b5096977ce76a809d9',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-symfonymailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
