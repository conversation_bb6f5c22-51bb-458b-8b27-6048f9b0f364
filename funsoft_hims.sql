-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Jul 09, 2025 at 02:27 PM
-- Server version: 10.4.18-MariaDB
-- PHP Version: 7.4.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `funsoft_hims`
--

-- --------------------------------------------------------

--
-- Table structure for table `banks`
--

CREATE TABLE `banks` (
  `bank_id` int(11) NOT NULL,
  `bank_name` varchar(255) NOT NULL,
  `bank_code` varchar(20) DEFAULT NULL,
  `branch_name` varchar(255) DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `account_holder_name` varchar(255) DEFAULT NULL,
  `ifsc_code` varchar(20) DEFAULT NULL,
  `swift_code` varchar(20) DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `bill_items`
--

CREATE TABLE `bill_items` (
  `item_id` int(11) NOT NULL,
  `bill_id` int(11) NOT NULL,
  `service_id` int(11) DEFAULT NULL,
  `item_name` varchar(255) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_percentage` decimal(5,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `branch_id` int(11) NOT NULL,
  `hospital_id` int(11) NOT NULL,
  `branch_name` varchar(255) NOT NULL,
  `branch_code` varchar(20) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `manager_name` varchar(255) DEFAULT NULL,
  `is_main_branch` tinyint(1) DEFAULT 0,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `branches`
--

INSERT INTO `branches` (`branch_id`, `hospital_id`, `branch_name`, `branch_code`, `address`, `city`, `state`, `postal_code`, `phone`, `email`, `manager_name`, `is_main_branch`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'General Hospital Main', 'GH001', '123 Main Street', 'Nairobi', 'Nairobi County', '00100', '+254-700-123-456', '<EMAIL>', 'Dr. John Kamau', 1, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(2, 1, 'General Hospital Westlands', 'GH002', '456 Westlands Road', 'Nairobi', 'Nairobi County', '00600', '+254-700-123-457', '<EMAIL>', 'Dr. Mary Wanjiku', 0, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(3, 1, 'General Hospital Eastlands', 'GH003', '789 Eastlands Avenue', 'Nairobi', 'Nairobi County', '00200', '+254-700-123-458', '<EMAIL>', 'Dr. Peter Mutua', 0, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(4, 2, 'City Medical Main', 'CM001', '456 Uhuru Highway', 'Mombasa', 'Mombasa County', '80100', '+254-700-234-567', '<EMAIL>', 'Dr. Ahmed Hassan', 1, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(5, 2, 'City Medical Likoni', 'CM002', '123 Likoni Road', 'Mombasa', 'Mombasa County', '80100', '+254-700-234-568', '<EMAIL>', 'Dr. Fatuma Ali', 0, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(6, 3, 'National Referral Main', 'NR001', '789 Kenyatta Avenue', 'Kisumu', 'Kisumu County', '40100', '+***********-678', '<EMAIL>', 'Dr. Grace Otieno', 1, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `branch_services`
--

CREATE TABLE `branch_services` (
  `branch_service_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `department_id` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `tax_percentage` decimal(5,2) DEFAULT 0.00,
  `is_available` tinyint(1) DEFAULT 1,
  `is_blocked` tinyint(1) DEFAULT 0,
  `blocked_reason` text DEFAULT NULL,
  `blocked_by` int(11) DEFAULT NULL,
  `blocked_date` timestamp NULL DEFAULT NULL,
  `is_covered_by_scheme` tinyint(1) DEFAULT 0,
  `scheme_coverage_percentage` decimal(5,2) DEFAULT 0.00,
  `max_scheme_coverage_amount` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `cashpoints`
--

CREATE TABLE `cashpoints` (
  `cashpoint_id` int(11) NOT NULL,
  `cashpoint_name` varchar(255) NOT NULL,
  `cashpoint_code` varchar(20) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `opening_balance` decimal(10,2) DEFAULT 0.00,
  `current_balance` decimal(10,2) DEFAULT 0.00,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `cashpoint_allocations`
--

CREATE TABLE `cashpoint_allocations` (
  `allocation_id` int(11) NOT NULL,
  `cashpoint_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `allocated_date` date NOT NULL,
  `shift_start` time DEFAULT NULL,
  `shift_end` time DEFAULT NULL,
  `opening_balance` decimal(10,2) DEFAULT 0.00,
  `closing_balance` decimal(10,2) DEFAULT NULL,
  `status` enum('Active','Closed') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `clinics`
--

CREATE TABLE `clinics` (
  `clinic_id` int(11) NOT NULL,
  `clinic_name` varchar(255) NOT NULL,
  `clinic_code` varchar(20) NOT NULL,
  `department_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `capacity` int(11) DEFAULT 0,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `credit_slips`
--

CREATE TABLE `credit_slips` (
  `slip_id` int(11) NOT NULL,
  `slip_number` varchar(50) NOT NULL,
  `patient_id` int(11) NOT NULL,
  `scheme_id` int(11) DEFAULT NULL,
  `slip_date` date NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `approval_date` date DEFAULT NULL,
  `status` enum('Pending','Approved','Rejected','Used') DEFAULT 'Pending',
  `remarks` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Stand-in structure for view `daily_revenue_summary`
-- (See below for the actual view)
--
CREATE TABLE `daily_revenue_summary` (
`bill_date` date
,`branch_id` int(11)
,`branch_name` varchar(255)
,`total_bills` bigint(21)
,`total_revenue` decimal(32,2)
,`total_paid` decimal(32,2)
,`total_outstanding` decimal(32,2)
,`unique_patients` bigint(21)
);

-- --------------------------------------------------------

--
-- Table structure for table `departments`
--

CREATE TABLE `departments` (
  `department_id` int(11) NOT NULL,
  `department_name` varchar(255) NOT NULL,
  `department_code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `head_of_department` int(11) DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `departments`
--

INSERT INTO `departments` (`department_id`, `department_name`, `department_code`, `description`, `head_of_department`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Emergency Medicine', 'EM001', 'Emergency and trauma care services', NULL, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(2, 'Internal Medicine', 'IM001', 'General internal medicine and consultation', 3, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(3, 'Surgery', 'SG001', 'General and specialized surgical procedures', 5, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(4, 'Pediatrics', 'PD001', 'Child health and medical care', 6, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(5, 'Obstetrics & Gynecology', 'OG001', 'Womens health and maternity services', NULL, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(6, 'Cardiology', 'CD001', 'Heart and cardiovascular care', 7, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(7, 'Orthopedics', 'OR001', 'Bone and joint care', NULL, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(8, 'Neurology', 'NL001', 'Brain and nervous system care', NULL, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(9, 'Radiology', 'RD001', 'Medical imaging and diagnostics', NULL, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(10, 'Laboratory', 'LB001', 'Clinical laboratory services', 16, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(11, 'Pharmacy', 'PH001', 'Medication dispensing and management', 14, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(12, 'Administration', 'AD001', 'Hospital administration and management', 1, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(13, 'Nursing', 'NS001', 'Nursing services and patient care', 8, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(14, 'Physiotherapy', 'PT001', 'Physical therapy and rehabilitation', NULL, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(15, 'Psychiatry', 'PS001', 'Mental health and psychiatric care', NULL, 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `document_settings`
--

CREATE TABLE `document_settings` (
  `setting_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `document_type` enum('Invoice','Receipt','Prescription','Lab_Report','Discharge_Summary') NOT NULL,
  `header_text` text DEFAULT NULL,
  `footer_text` text DEFAULT NULL,
  `prefix` varchar(10) DEFAULT NULL,
  `suffix` varchar(10) DEFAULT NULL,
  `current_number` int(11) DEFAULT 1,
  `logo_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `hospitals`
--

CREATE TABLE `hospitals` (
  `hospital_id` int(11) NOT NULL,
  `hospital_name` varchar(255) NOT NULL,
  `registration_number` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `license_number` varchar(100) DEFAULT NULL,
  `established_date` date DEFAULT NULL,
  `hospital_type` enum('Government','Private','Trust','Corporate') DEFAULT 'Private',
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `hospitals`
--

INSERT INTO `hospitals` (`hospital_id`, `hospital_name`, `registration_number`, `address`, `city`, `state`, `country`, `postal_code`, `phone`, `email`, `website`, `license_number`, `established_date`, `hospital_type`, `status`, `created_at`, `updated_at`) VALUES
(1, 'General Hospital', 'REG001', '123 Main Street', 'Nairobi', 'Nairobi County', 'Kenya', '00100', '+254-700-123-456', '<EMAIL>', 'www.generalhospital.co.ke', 'LIC001', '2010-01-15', 'Private', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(2, 'City Medical Center', 'REG002', '456 Uhuru Highway', 'Mombasa', 'Mombasa County', 'Kenya', '80100', '+254-700-234-567', '<EMAIL>', 'www.citymedical.co.ke', 'LIC002', '2015-03-20', 'Private', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(3, 'National Referral Hospital', 'REG003', '789 Kenyatta Avenue', 'Kisumu', 'Kisumu County', 'Kenya', '40100', '+***********-678', '<EMAIL>', 'www.nationalreferral.co.ke', 'LIC003', '2005-05-10', 'Government', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `master_services`
--

CREATE TABLE `master_services` (
  `service_id` int(11) NOT NULL,
  `service_name` varchar(255) NOT NULL,
  `service_code` varchar(20) NOT NULL,
  `service_category` enum('Consultation','Diagnostic','Procedure','Surgery','Therapy','Other') NOT NULL,
  `description` text DEFAULT NULL,
  `base_price` decimal(10,2) NOT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `nok_relationships`
--

CREATE TABLE `nok_relationships` (
  `relationship_id` int(11) NOT NULL,
  `relationship_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `nok_relationships`
--

INSERT INTO `nok_relationships` (`relationship_id`, `relationship_name`, `description`, `status`, `created_at`) VALUES
(1, 'Uncle', 'Paternal or maternal uncle', 'Active', '2025-07-09 12:18:06'),
(2, 'Aunt', 'Paternal or maternal aunt', 'Active', '2025-07-09 12:18:06'),
(3, 'Cousin', 'Cousin relationship', 'Active', '2025-07-09 12:18:06'),
(4, 'Grandfather', 'Paternal or maternal grandfather', 'Active', '2025-07-09 12:18:06'),
(5, 'Grandmother', 'Paternal or maternal grandmother', 'Active', '2025-07-09 12:18:06'),
(6, 'Son-in-law', 'Male spouse of child', 'Active', '2025-07-09 12:18:06'),
(7, 'Daughter-in-law', 'Female spouse of child', 'Active', '2025-07-09 12:18:06'),
(8, 'Nephew', 'Son of sibling', 'Active', '2025-07-09 12:18:06'),
(9, 'Niece', 'Daughter of sibling', 'Active', '2025-07-09 12:18:06'),
(10, 'Colleague', 'Work colleague', 'Active', '2025-07-09 12:18:06'),
(11, 'Neighbor', 'Neighbor', 'Active', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `patients`
--

CREATE TABLE `patients` (
  `patient_id` int(11) NOT NULL,
  `patient_number` varchar(50) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `middle_name` varchar(100) DEFAULT NULL,
  `date_of_birth` date NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `nationality` varchar(100) DEFAULT NULL,
  `occupation` varchar(100) DEFAULT NULL,
  `marital_status` enum('Single','Married','Divorced','Widowed','Other') DEFAULT NULL,
  `blood_group` enum('A+','A-','B+','B-','AB+','AB-','O+','O-','Unknown') DEFAULT NULL,
  `allergies` text DEFAULT NULL,
  `medical_history` text DEFAULT NULL,
  `emergency_contact_name` varchar(255) DEFAULT NULL,
  `emergency_contact_phone` varchar(20) DEFAULT NULL,
  `emergency_contact_relationship_id` int(11) DEFAULT NULL,
  `registration_date` date NOT NULL,
  `branch_id` int(11) NOT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `patient_bills`
--

CREATE TABLE `patient_bills` (
  `bill_id` int(11) NOT NULL,
  `bill_number` varchar(50) NOT NULL,
  `visit_id` int(11) NOT NULL,
  `patient_id` int(11) NOT NULL,
  `bill_date` date NOT NULL,
  `bill_time` time NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `balance_amount` decimal(10,2) NOT NULL,
  `payment_status` enum('Pending','Partial','Paid','Cancelled') DEFAULT 'Pending',
  `bill_type` enum('Consultation','Diagnostic','Pharmacy','Procedure','Surgery','Emergency') NOT NULL,
  `cashpoint_id` int(11) DEFAULT NULL,
  `billed_by` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `patient_limits`
--

CREATE TABLE `patient_limits` (
  `limit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `patient_type` enum('Outpatient','Inpatient') NOT NULL,
  `limit_type` enum('Daily','Monthly','Annual') NOT NULL,
  `limit_amount` decimal(10,2) NOT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `patient_nok`
--

CREATE TABLE `patient_nok` (
  `nok_id` int(11) NOT NULL,
  `patient_id` int(11) NOT NULL,
  `nok_name` varchar(255) NOT NULL,
  `relationship_id` int(11) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `is_primary` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Stand-in structure for view `patient_summary`
-- (See below for the actual view)
--
CREATE TABLE `patient_summary` (
`patient_id` int(11)
,`patient_number` varchar(50)
,`patient_name` varchar(201)
,`date_of_birth` date
,`gender` enum('Male','Female','Other')
,`phone` varchar(20)
,`email` varchar(100)
,`total_visits` bigint(21)
,`total_bills` bigint(21)
,`total_billed_amount` decimal(32,2)
,`total_paid_amount` decimal(32,2)
,`total_balance_amount` decimal(32,2)
,`branch_id` int(11)
,`branch_name` varchar(255)
);

-- --------------------------------------------------------

--
-- Table structure for table `patient_visits`
--

CREATE TABLE `patient_visits` (
  `visit_id` int(11) NOT NULL,
  `patient_id` int(11) NOT NULL,
  `visit_number` varchar(50) NOT NULL,
  `visit_date` date NOT NULL,
  `visit_time` time NOT NULL,
  `visit_type` enum('New','Revisit','Follow-up','Emergency') NOT NULL,
  `department_id` int(11) NOT NULL,
  `clinic_id` int(11) DEFAULT NULL,
  `attending_doctor_id` int(11) DEFAULT NULL,
  `chief_complaint` text DEFAULT NULL,
  `vital_signs` text DEFAULT NULL,
  `diagnosis` text DEFAULT NULL,
  `treatment_plan` text DEFAULT NULL,
  `next_visit_date` date DEFAULT NULL,
  `visit_status` enum('Scheduled','In-Progress','Completed','Cancelled') DEFAULT 'Scheduled',
  `branch_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `payment_id` int(11) NOT NULL,
  `bill_id` int(11) NOT NULL,
  `payment_number` varchar(50) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_time` time NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_mode_id` int(11) NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `cashpoint_id` int(11) DEFAULT NULL,
  `received_by` int(11) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Triggers `payments`
--
DELIMITER $$
CREATE TRIGGER `update_bill_balance` AFTER INSERT ON `payments` FOR EACH ROW BEGIN
    UPDATE patient_bills 
    SET paid_amount = paid_amount + NEW.amount,
        balance_amount = total_amount - (paid_amount + NEW.amount),
        payment_status = CASE 
            WHEN (paid_amount + NEW.amount) >= total_amount THEN 'Paid'
            WHEN (paid_amount + NEW.amount) > 0 THEN 'Partial'
            ELSE 'Pending'
        END
    WHERE bill_id = NEW.bill_id;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `update_cashpoint_balance` AFTER INSERT ON `payments` FOR EACH ROW BEGIN
    IF NEW.cashpoint_id IS NOT NULL THEN
        UPDATE cashpoints 
        SET current_balance = current_balance + NEW.amount
        WHERE cashpoint_id = NEW.cashpoint_id;
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `payment_modes`
--

CREATE TABLE `payment_modes` (
  `payment_mode_id` int(11) NOT NULL,
  `mode_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `requires_reference` tinyint(1) DEFAULT 0,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `payment_modes`
--

INSERT INTO `payment_modes` (`payment_mode_id`, `mode_name`, `description`, `requires_reference`, `status`, `created_at`) VALUES
(1, 'M-Pesa', 'M-Pesa mobile money transfer', 1, 'Active', '2025-07-09 12:18:06'),
(2, 'Airtel Money', 'Airtel Money mobile payment', 1, 'Active', '2025-07-09 12:18:06'),
(3, 'Equitel', 'Equitel mobile banking', 1, 'Active', '2025-07-09 12:18:06'),
(4, 'T-Kash', 'T-Kash mobile payment', 1, 'Active', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `permission_id` int(11) NOT NULL,
  `permission_name` varchar(100) NOT NULL,
  `module_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`permission_id`, `permission_name`, `module_name`, `description`, `created_at`) VALUES
(1, 'CREATE_PATIENT', 'Patient Management', 'Create new patient records', '2025-07-09 12:18:06'),
(2, 'VIEW_PATIENT', 'Patient Management', 'View patient records', '2025-07-09 12:18:06'),
(3, 'UPDATE_PATIENT', 'Patient Management', 'Update patient records', '2025-07-09 12:18:06'),
(4, 'DELETE_PATIENT', 'Patient Management', 'Delete patient records', '2025-07-09 12:18:06'),
(5, 'CREATE_VISIT', 'Visit Management', 'Create new patient visits', '2025-07-09 12:18:06'),
(6, 'VIEW_VISIT', 'Visit Management', 'View patient visits', '2025-07-09 12:18:06'),
(7, 'UPDATE_VISIT', 'Visit Management', 'Update patient visits', '2025-07-09 12:18:06'),
(8, 'CREATE_BILL', 'Billing', 'Create new bills', '2025-07-09 12:18:06'),
(9, 'VIEW_BILL', 'Billing', 'View bills', '2025-07-09 12:18:06'),
(10, 'UPDATE_BILL', 'Billing', 'Update bills', '2025-07-09 12:18:06'),
(11, 'PROCESS_PAYMENT', 'Billing', 'Process payments', '2025-07-09 12:18:06'),
(12, 'MANAGE_INVENTORY', 'Pharmacy', 'Manage pharmacy inventory', '2025-07-09 12:18:06'),
(13, 'SELL_MEDICATION', 'Pharmacy', 'Sell medications', '2025-07-09 12:18:06'),
(14, 'MANAGE_USERS', 'User Management', 'Manage system users', '2025-07-09 12:18:06'),
(15, 'MANAGE_ROLES', 'User Management', 'Manage user roles', '2025-07-09 12:18:06'),
(16, 'VIEW_REPORTS', 'Reports', 'View system reports', '2025-07-09 12:18:06'),
(17, 'SYSTEM_SETTINGS', 'Settings', 'Manage system settings', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `pharmacy_inventory`
--

CREATE TABLE `pharmacy_inventory` (
  `inventory_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `batch_number` varchar(100) DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `quantity_in_stock` int(11) NOT NULL DEFAULT 0,
  `cost_price` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `supplier_name` varchar(255) DEFAULT NULL,
  `received_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `pharmacy_items`
--

CREATE TABLE `pharmacy_items` (
  `item_id` int(11) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `item_code` varchar(50) NOT NULL,
  `generic_name` varchar(255) DEFAULT NULL,
  `brand_name` varchar(255) DEFAULT NULL,
  `manufacturer` varchar(255) DEFAULT NULL,
  `category` enum('Tablet','Capsule','Syrup','Injection','Cream','Drops','Other') NOT NULL,
  `strength` varchar(100) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `pack_size` int(11) DEFAULT 1,
  `minimum_stock_level` int(11) DEFAULT 0,
  `maximum_stock_level` int(11) DEFAULT 0,
  `reorder_level` int(11) DEFAULT 0,
  `storage_conditions` text DEFAULT NULL,
  `status` enum('Active','Inactive','Discontinued') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `pharmacy_orders`
--

CREATE TABLE `pharmacy_orders` (
  `order_id` int(11) NOT NULL,
  `order_number` varchar(50) NOT NULL,
  `supplier_name` varchar(255) NOT NULL,
  `order_date` date NOT NULL,
  `expected_delivery_date` date DEFAULT NULL,
  `order_status` enum('Pending','Confirmed','Partially_Received','Completed','Cancelled') DEFAULT 'Pending',
  `total_amount` decimal(10,2) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `ordered_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `pharmacy_order_items`
--

CREATE TABLE `pharmacy_order_items` (
  `order_item_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity_ordered` int(11) NOT NULL,
  `quantity_received` int(11) DEFAULT 0,
  `unit_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `batch_number` varchar(100) DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `received_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `pharmacy_sales`
--

CREATE TABLE `pharmacy_sales` (
  `sale_id` int(11) NOT NULL,
  `sale_number` varchar(50) NOT NULL,
  `patient_id` int(11) DEFAULT NULL,
  `prescription_number` varchar(50) DEFAULT NULL,
  `sale_date` date NOT NULL,
  `sale_time` time NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `net_amount` decimal(10,2) NOT NULL,
  `payment_mode_id` int(11) NOT NULL,
  `cashpoint_id` int(11) DEFAULT NULL,
  `sold_by` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `pharmacy_sale_items`
--

CREATE TABLE `pharmacy_sale_items` (
  `sale_item_id` int(11) NOT NULL,
  `sale_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity_sold` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_percentage` decimal(5,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `batch_number` varchar(100) DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Triggers `pharmacy_sale_items`
--
DELIMITER $$
CREATE TRIGGER `update_inventory_after_sale` AFTER INSERT ON `pharmacy_sale_items` FOR EACH ROW BEGIN
    UPDATE pharmacy_inventory 
    SET quantity_in_stock = quantity_in_stock - NEW.quantity_sold
    WHERE item_id = NEW.item_id 
    AND batch_number = NEW.batch_number;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Stand-in structure for view `pharmacy_stock_status`
-- (See below for the actual view)
--
CREATE TABLE `pharmacy_stock_status` (
`item_id` int(11)
,`item_name` varchar(255)
,`item_code` varchar(50)
,`category` enum('Tablet','Capsule','Syrup','Injection','Cream','Drops','Other')
,`minimum_stock_level` int(11)
,`reorder_level` int(11)
,`branch_id` int(11)
,`branch_name` varchar(255)
,`current_stock` decimal(32,0)
,`stock_status` varchar(9)
);

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `role_id` int(11) NOT NULL,
  `role_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`role_id`, `role_name`, `description`, `status`, `created_at`) VALUES
(1, 'Super Admin', 'Full system access and administration', 'Active', '2025-07-09 12:18:06'),
(2, 'Hospital Admin', 'Hospital level administration access', 'Active', '2025-07-09 12:18:06'),
(3, 'Branch Admin', 'Branch level administration access', 'Active', '2025-07-09 12:18:06'),
(4, 'Doctor', 'Medical practitioner with patient care access', 'Active', '2025-07-09 12:18:06'),
(5, 'Nurse', 'Nursing staff with patient care access', 'Active', '2025-07-09 12:18:06'),
(6, 'Receptionist', 'Front desk operations and patient registration', 'Active', '2025-07-09 12:18:06'),
(7, 'Pharmacist', 'Pharmacy operations and medication management', 'Active', '2025-07-09 12:18:06'),
(8, 'Lab Technician', 'Laboratory operations and test management', 'Active', '2025-07-09 12:18:06'),
(9, 'Accountant', 'Financial operations and billing management', 'Active', '2025-07-09 12:18:06'),
(10, 'Manager', 'Management level access and oversight', 'Active', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `role_permissions`
--

CREATE TABLE `role_permissions` (
  `role_permission_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `role_permissions`
--

INSERT INTO `role_permissions` (`role_permission_id`, `role_id`, `permission_id`, `created_at`) VALUES
(1, 1, 1, '2025-07-09 12:18:06'),
(2, 1, 2, '2025-07-09 12:18:06'),
(3, 1, 3, '2025-07-09 12:18:06'),
(4, 1, 4, '2025-07-09 12:18:06'),
(5, 1, 5, '2025-07-09 12:18:06'),
(6, 1, 6, '2025-07-09 12:18:06'),
(7, 1, 7, '2025-07-09 12:18:06'),
(8, 1, 8, '2025-07-09 12:18:06'),
(9, 1, 9, '2025-07-09 12:18:06'),
(10, 1, 10, '2025-07-09 12:18:06'),
(11, 1, 11, '2025-07-09 12:18:06'),
(12, 1, 12, '2025-07-09 12:18:06'),
(13, 1, 13, '2025-07-09 12:18:06'),
(14, 1, 14, '2025-07-09 12:18:06'),
(15, 1, 15, '2025-07-09 12:18:06'),
(16, 1, 16, '2025-07-09 12:18:06'),
(17, 1, 17, '2025-07-09 12:18:06'),
(18, 4, 1, '2025-07-09 12:18:06'),
(19, 4, 2, '2025-07-09 12:18:06'),
(20, 4, 3, '2025-07-09 12:18:06'),
(21, 4, 5, '2025-07-09 12:18:06'),
(22, 4, 6, '2025-07-09 12:18:06'),
(23, 4, 7, '2025-07-09 12:18:06'),
(24, 4, 9, '2025-07-09 12:18:06'),
(25, 4, 16, '2025-07-09 12:18:06'),
(26, 5, 1, '2025-07-09 12:18:06'),
(27, 5, 2, '2025-07-09 12:18:06'),
(28, 5, 3, '2025-07-09 12:18:06'),
(29, 5, 5, '2025-07-09 12:18:06'),
(30, 5, 6, '2025-07-09 12:18:06'),
(31, 5, 7, '2025-07-09 12:18:06'),
(32, 5, 9, '2025-07-09 12:18:06'),
(33, 6, 1, '2025-07-09 12:18:06'),
(34, 6, 2, '2025-07-09 12:18:06'),
(35, 6, 3, '2025-07-09 12:18:06'),
(36, 6, 5, '2025-07-09 12:18:06'),
(37, 6, 6, '2025-07-09 12:18:06'),
(38, 6, 9, '2025-07-09 12:18:06'),
(39, 7, 2, '2025-07-09 12:18:06'),
(40, 7, 12, '2025-07-09 12:18:06'),
(41, 7, 13, '2025-07-09 12:18:06'),
(42, 7, 16, '2025-07-09 12:18:06'),
(43, 8, 2, '2025-07-09 12:18:06'),
(44, 8, 6, '2025-07-09 12:18:06'),
(45, 8, 16, '2025-07-09 12:18:06'),
(46, 9, 2, '2025-07-09 12:18:06'),
(47, 9, 8, '2025-07-09 12:18:06'),
(48, 9, 9, '2025-07-09 12:18:06'),
(49, 9, 10, '2025-07-09 12:18:06'),
(50, 9, 11, '2025-07-09 12:18:06'),
(51, 9, 16, '2025-07-09 12:18:06'),
(52, 10, 1, '2025-07-09 12:18:06'),
(53, 10, 2, '2025-07-09 12:18:06'),
(54, 10, 3, '2025-07-09 12:18:06'),
(55, 10, 5, '2025-07-09 12:18:06'),
(56, 10, 6, '2025-07-09 12:18:06'),
(57, 10, 7, '2025-07-09 12:18:06'),
(58, 10, 8, '2025-07-09 12:18:06'),
(59, 10, 9, '2025-07-09 12:18:06'),
(60, 10, 10, '2025-07-09 12:18:06'),
(61, 10, 11, '2025-07-09 12:18:06'),
(62, 10, 12, '2025-07-09 12:18:06'),
(63, 10, 13, '2025-07-09 12:18:06'),
(64, 10, 16, '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `schemes`
--

CREATE TABLE `schemes` (
  `scheme_id` int(11) NOT NULL,
  `provider_id` int(11) NOT NULL,
  `scheme_name` varchar(255) NOT NULL,
  `scheme_code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `coverage_percentage` decimal(5,2) DEFAULT 0.00,
  `max_coverage_amount` decimal(10,2) DEFAULT NULL,
  `copay_amount` decimal(10,2) DEFAULT 0.00,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `scheme_providers`
--

CREATE TABLE `scheme_providers` (
  `provider_id` int(11) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `provider_code` varchar(20) NOT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `scheme_service_coverage`
--

CREATE TABLE `scheme_service_coverage` (
  `coverage_id` int(11) NOT NULL,
  `scheme_id` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `is_covered` tinyint(1) DEFAULT 0,
  `coverage_percentage` decimal(5,2) DEFAULT 0.00,
  `max_coverage_amount` decimal(10,2) DEFAULT NULL,
  `copay_amount` decimal(10,2) DEFAULT 0.00,
  `requires_pre_authorization` tinyint(1) DEFAULT 0,
  `coverage_notes` text DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Stand-in structure for view `service_availability_coverage`
-- (See below for the actual view)
--
CREATE TABLE `service_availability_coverage` (
`branch_service_id` int(11)
,`branch_id` int(11)
,`branch_name` varchar(255)
,`service_id` int(11)
,`service_name` varchar(255)
,`service_code` varchar(20)
,`service_category` enum('Consultation','Diagnostic','Procedure','Surgery','Therapy','Other')
,`department_name` varchar(255)
,`price` decimal(10,2)
,`discount_percentage` decimal(5,2)
,`tax_percentage` decimal(5,2)
,`is_available` tinyint(1)
,`is_blocked` tinyint(1)
,`blocked_reason` text
,`blocked_by_name` varchar(201)
,`blocked_date` timestamp
,`is_covered_by_scheme` tinyint(1)
,`scheme_coverage_percentage` decimal(5,2)
,`max_scheme_coverage_amount` decimal(10,2)
,`scheme_specific_coverages` bigint(21)
);

-- --------------------------------------------------------

--
-- Table structure for table `staff`
--

CREATE TABLE `staff` (
  `staff_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `employee_id` varchar(50) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `middle_name` varchar(100) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('Male','Female','Other') DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `emergency_contact_name` varchar(255) DEFAULT NULL,
  `emergency_contact_phone` varchar(20) DEFAULT NULL,
  `hire_date` date NOT NULL,
  `job_title` varchar(100) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `branch_id` int(11) NOT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `status` enum('Active','Inactive','Terminated') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `staff`
--

INSERT INTO `staff` (`staff_id`, `user_id`, `employee_id`, `first_name`, `last_name`, `middle_name`, `date_of_birth`, `gender`, `phone`, `email`, `address`, `city`, `state`, `postal_code`, `emergency_contact_name`, `emergency_contact_phone`, `hire_date`, `job_title`, `department_id`, `branch_id`, `salary`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'EMP001', 'System', 'Administrator', NULL, '1980-01-15', 'Male', '+254-700-000-001', '<EMAIL>', 'Admin Office', 'Nairobi', 'Nairobi County', NULL, 'Admin Contact', '+254-700-000-011', '2020-01-01', 'System Administrator', 12, 1, '150000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(2, 2, 'EMP002', 'Super', 'Admin', NULL, '1975-05-20', 'Male', '+254-700-000-002', '<EMAIL>', 'Head Office', 'Nairobi', 'Nairobi County', NULL, 'Super Contact', '+254-700-000-012', '2020-01-01', 'Super Administrator', 12, 1, '200000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(3, 3, 'GH001', 'John', 'Kamau', NULL, '1975-03-10', 'Male', '+254-700-001-001', '<EMAIL>', 'Karen Estate', 'Nairobi', 'Nairobi County', NULL, 'Jane Kamau', '+254-700-001-011', '2015-01-15', 'Chief Medical Officer', 2, 1, '180000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(4, 4, 'GH002', 'Mary', 'Wanjiku', NULL, '1980-07-22', 'Female', '+254-700-001-002', '<EMAIL>', 'Westlands', 'Nairobi', 'Nairobi County', NULL, 'Peter Wanjiku', '+254-700-001-012', '2016-03-01', 'Senior Physician', 2, 1, '150000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(5, 5, 'GH003', 'Peter', 'Mutua', NULL, '1978-11-05', 'Male', '+254-700-001-003', '<EMAIL>', 'Eastlands', 'Nairobi', 'Nairobi County', NULL, 'Grace Mutua', '+254-700-001-013', '2017-05-15', 'Surgeon', 3, 1, '200000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(6, 6, 'GH004', 'Alice', 'Akinyi', NULL, '1982-09-18', 'Female', '+254-700-001-004', '<EMAIL>', 'Kibera', 'Nairobi', 'Nairobi County', NULL, 'John Akinyi', '+254-700-001-014', '2018-02-01', 'Pediatrician', 4, 1, '140000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(7, 7, 'GH005', 'James', 'Ochieng', NULL, '1976-12-30', 'Male', '+254-700-001-005', '<EMAIL>', 'Kawangware', 'Nairobi', 'Nairobi County', NULL, 'Mary Ochieng', '+254-700-001-015', '2019-01-10', 'Cardiologist', 6, 1, '170000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(8, 8, 'GH101', 'Nancy', 'Njeri', NULL, '1985-04-12', 'Female', '+254-700-001-101', '<EMAIL>', 'Dagoretti', 'Nairobi', 'Nairobi County', NULL, 'Samuel Njeri', '+254-700-001-111', '2018-06-01', 'Senior Nurse', 13, 1, '80000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(9, 9, 'GH102', 'Sarah', 'Mwangi', NULL, '1987-08-25', 'Female', '+254-700-001-102', '<EMAIL>', 'Mathare', 'Nairobi', 'Nairobi County', NULL, 'David Mwangi', '+254-700-001-112', '2019-03-15', 'Registered Nurse', 13, 1, '70000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(10, 10, 'GH103', 'Rose', 'Kimani', NULL, '1983-06-08', 'Female', '+254-700-001-103', '<EMAIL>', 'Huruma', 'Nairobi', 'Nairobi County', NULL, 'Paul Kimani', '+254-700-001-113', '2020-01-20', 'Nurse Practitioner', 13, 1, '75000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(11, 11, 'GH104', 'Elizabeth', 'Waweru', NULL, '1986-02-14', 'Female', '+254-700-001-104', '<EMAIL>', 'Pangani', 'Nairobi', 'Nairobi County', NULL, 'Michael Waweru', '+254-700-001-114', '2020-05-10', 'ICU Nurse', 13, 1, '85000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(12, 12, 'GH201', 'Mary', 'Wairimu', NULL, '1990-10-03', 'Female', '+254-700-001-201', '<EMAIL>', 'Kariobangi', 'Nairobi', 'Nairobi County', NULL, 'John Wairimu', '+254-700-001-211', '2019-08-01', 'Senior Receptionist', 12, 1, '50000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(13, 13, 'GH202', 'Jane', 'Muthoni', NULL, '1992-12-20', 'Female', '+254-700-001-202', '<EMAIL>', 'Kayole', 'Nairobi', 'Nairobi County', NULL, 'Peter Muthoni', '+254-700-001-212', '2020-11-15', 'Receptionist', 12, 1, '45000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(14, 14, 'GH301', 'Kenneth', 'Kiptoo', NULL, '1981-05-17', 'Male', '+254-700-001-301', '<EMAIL>', 'Langata', 'Nairobi', 'Nairobi County', NULL, 'Ruth Kiptoo', '+254-700-001-311', '2017-07-01', 'Chief Pharmacist', 11, 1, '120000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(15, 15, 'GH302', 'Wanjiru', 'Ndung\'u', NULL, '1984-09-28', 'Female', '+254-700-001-302', '<EMAIL>', 'Riruta', 'Nairobi', 'Nairobi County', NULL, 'Stephen Ndung\'u', '+254-700-001-312', '2018-09-01', 'Pharmacist', 11, 1, '100000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(16, 16, 'GH401', 'Kevin', 'Kiprotich', NULL, '1988-01-11', 'Male', '+254-700-001-401', '<EMAIL>', 'Embakasi', 'Nairobi', 'Nairobi County', NULL, 'Agnes Kiprotich', '+254-700-001-411', '2019-04-01', 'Senior Lab Technician', 10, 1, '90000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(17, 17, 'GH402', 'Wilson', 'Wanjala', NULL, '1985-03-07', 'Male', '+254-700-001-402', '<EMAIL>', 'Kasarani', 'Nairobi', 'Nairobi County', NULL, 'Mercy Wanjala', '+254-700-001-412', '2020-02-15', 'Lab Technician', 10, 1, '75000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(18, 18, 'GH501', 'Kennedy', 'Kimutai', NULL, '1979-11-23', 'Male', '+254-700-001-501', '<EMAIL>', 'Roysambu', 'Nairobi', 'Nairobi County', NULL, 'Esther Kimutai', '+254-700-001-511', '2016-06-01', 'Senior Accountant', 12, 1, '110000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(19, 19, 'GH502', 'Wanjiku', 'Kamau', NULL, '1983-07-16', 'Female', '+254-700-001-502', '<EMAIL>', 'Thika Road', 'Nairobi', 'Nairobi County', NULL, 'Francis Kamau', '+254-700-001-512', '2018-10-01', 'Accountant', 12, 1, '95000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(20, 20, 'GH601', 'Kennedy', 'Kipchoge', NULL, '1974-08-02', 'Male', '+254-700-001-601', '<EMAIL>', 'Kileleshwa', 'Nairobi', 'Nairobi County', NULL, 'Joyce Kipchoge', '+254-700-001-611', '2014-12-01', 'Hospital Manager', 12, 1, '250000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(21, 21, 'GH2001', 'Nicholas', 'Njoroge', NULL, '1977-04-15', 'Male', '+254-700-002-001', '<EMAIL>', 'Westlands', 'Nairobi', 'Nairobi County', NULL, 'Catherine Njoroge', '+254-700-002-011', '2016-02-01', 'Branch Medical Officer', 2, 2, '160000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(22, 22, 'GH2002', 'Wanjiku', 'Nyambura', NULL, '1986-06-30', 'Female', '+254-700-002-101', '<EMAIL>', 'Parklands', 'Nairobi', 'Nairobi County', NULL, 'James Nyambura', '+254-700-002-111', '2017-08-15', 'Senior Nurse', 13, 2, '78000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(23, 23, 'GH2003', 'Grace', 'Wanjiru', NULL, '1991-01-25', 'Female', '+254-700-002-201', '<EMAIL>', 'Highridge', 'Nairobi', 'Nairobi County', NULL, 'Samuel Wanjiru', '+254-700-002-211', '2018-05-01', 'Receptionist', 12, 2, '47000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(24, 24, 'CM001', 'Ahmed', 'Hassan', NULL, '1972-02-28', 'Male', '+254-700-003-001', '<EMAIL>', 'Nyali', 'Mombasa', 'Mombasa County', NULL, 'Khadija Hassan', '+254-700-003-011', '2015-03-01', 'Chief Medical Officer', 2, 4, '175000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(25, 25, 'CM002', 'Fatuma', 'Ali', NULL, '1979-12-10', 'Female', '+254-700-003-002', '<EMAIL>', 'Bamburi', 'Mombasa', 'Mombasa County', NULL, 'Ali Mohamed', '+254-700-003-012', '2016-07-01', 'Senior Physician', 2, 4, '145000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(26, 26, 'CM101', 'Amina', 'Mohamed', NULL, '1985-09-05', 'Female', '+254-700-003-101', '<EMAIL>', 'Likoni', 'Mombasa', 'Mombasa County', NULL, 'Hassan Mohamed', '+254-700-003-111', '2017-11-01', 'Registered Nurse', 13, 4, '72000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(27, 27, 'CM301', 'Omar', 'Hassan', NULL, '1982-11-18', 'Male', '+254-700-003-301', '<EMAIL>', 'Tudor', 'Mombasa', 'Mombasa County', NULL, 'Zainab Hassan', '+254-700-003-311', '2018-01-15', 'Pharmacist', 11, 4, '105000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(28, 28, 'NR001', 'Grace', 'Otieno', NULL, '1976-05-12', 'Female', '+254-700-004-001', '<EMAIL>', 'Milimani', 'Kisumu', 'Kisumu County', NULL, 'Peter Otieno', '+254-700-004-011', '2014-08-01', 'Chief Medical Officer', 2, 6, '170000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(29, 29, 'NR002', 'Joseph', 'Okoth', NULL, '1978-10-07', 'Male', '+254-700-004-002', '<EMAIL>', 'Kondele', 'Kisumu', 'Kisumu County', NULL, 'Mary Okoth', '+254-700-004-012', '2015-12-01', 'Senior Surgeon', 3, 6, '180000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(30, 30, 'NR101', 'Agnes', 'Anyango', NULL, '1987-03-22', 'Female', '+254-700-004-101', '<EMAIL>', 'Mamboleo', 'Kisumu', 'Kisumu County', NULL, 'Daniel Anyango', '+254-700-004-111', '2016-04-01', 'Senior Nurse', 13, 6, '76000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(31, 31, 'NR401', 'Oduya', 'Omondi', NULL, '1984-08-14', 'Male', '+254-700-004-401', '<EMAIL>', 'Nyamasaria', 'Kisumu', 'Kisumu County', NULL, 'Beatrice Omondi', '+254-700-004-411', '2017-06-15', 'Lab Technician', 10, 6, '77000.00', 'Active', '2025-07-09 12:18:06', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `user_type` enum('Admin','Doctor','Nurse','Receptionist','Pharmacist','Lab_Tech','Accountant','Manager') NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `email`, `password_hash`, `first_name`, `last_name`, `phone`, `user_type`, `branch_id`, `is_active`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'System', 'Administrator', '+254-700-000-001', 'Admin', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(2, 'superadmin', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Super', 'Admin', '+254-700-000-002', 'Admin', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(3, 'dr.kamau', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'John', 'Kamau', '+254-700-001-001', 'Doctor', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(4, 'dr.wanjiku', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Mary', 'Wanjiku', '+254-700-001-002', 'Doctor', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(5, 'dr.mutua', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Peter', 'Mutua', '+254-700-001-003', 'Doctor', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(6, 'dr.akinyi', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Alice', 'Akinyi', '+254-700-001-004', 'Doctor', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(7, 'dr.ochieng', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'James', 'Ochieng', '+254-700-001-005', 'Doctor', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(8, 'nurse.njeri', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Nancy', 'Njeri', '+254-700-001-101', 'Nurse', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(9, 'nurse.mwangi', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Sarah', 'Mwangi', '+254-700-001-102', 'Nurse', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(10, 'nurse.kimani', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Rose', 'Kimani', '+254-700-001-103', 'Nurse', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(11, 'nurse.waweru', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Elizabeth', 'Waweru', '+254-700-001-104', 'Nurse', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(12, 'reception.mary', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Mary', 'Wairimu', '+254-700-001-201', 'Receptionist', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(13, 'reception.jane', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Jane', 'Muthoni', '+254-700-001-202', 'Receptionist', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(14, 'pharm.kiptoo', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Kenneth', 'Kiptoo', '+254-700-001-301', 'Pharmacist', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(15, 'pharm.wanjiru', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Wanjiru', 'Ndung\'u', '+254-700-001-302', 'Pharmacist', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(16, 'lab.kiprotich', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Kevin', 'Kiprotich', '+254-700-001-401', 'Lab_Tech', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(17, 'lab.wanjala', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Wilson', 'Wanjala', '+254-700-001-402', 'Lab_Tech', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(18, 'acc.kimutai', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Kennedy', 'Kimutai', '+254-700-001-501', 'Accountant', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(19, 'acc.wkamau', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Wanjiku', 'Kamau', '+254-700-001-502', 'Accountant', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(20, 'mgr.kipchoge', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Kennedy', 'Kipchoge', '+254-700-001-601', 'Manager', 1, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(21, 'dr.njoroge', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Nicholas', 'Njoroge', '+254-700-002-001', 'Doctor', 2, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(22, 'nurse.nyambura', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Wanjiku', 'Nyambura', '+254-700-002-101', 'Nurse', 2, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(23, 'reception.grace', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Grace', 'Wanjiru', '+254-700-002-201', 'Receptionist', 2, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(24, 'dr.hassan', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Ahmed', 'Hassan', '+254-700-003-001', 'Doctor', 4, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(25, 'dr.fatuma', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Fatuma', 'Ali', '+254-700-003-002', 'Doctor', 4, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(26, 'nurse.amina', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Amina', 'Mohamed', '+254-700-003-101', 'Nurse', 4, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(27, 'pharm.omar', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Omar', 'Hassan', '+254-700-003-301', 'Pharmacist', 4, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(28, 'dr.otieno', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Grace', 'Otieno', '+254-700-004-001', 'Doctor', 6, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(29, 'dr.okoth', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Joseph', 'Okoth', '+254-700-004-002', 'Doctor', 6, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(30, 'nurse.anyango', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Agnes', 'Anyango', '+254-700-004-101', 'Nurse', 6, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06'),
(31, 'lab.oduya', '<EMAIL>', '$2y$13$QxN8lEMTcI12zQhyqpgKLO.HGkNyBQsEJZOLj3mHiZGY.2QWYxLjm', 'Oduya', 'Omondi', '+254-700-004-401', 'Lab_Tech', 6, 1, NULL, '2025-07-09 12:18:06', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `user_roles`
--

CREATE TABLE `user_roles` (
  `user_role_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `assigned_date` date NOT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `user_roles`
--

INSERT INTO `user_roles` (`user_role_id`, `user_id`, `role_id`, `assigned_date`, `status`, `created_at`) VALUES
(1, 1, 1, '2020-01-01', 'Active', '2025-07-09 12:18:06'),
(2, 2, 1, '2020-01-01', 'Active', '2025-07-09 12:18:06'),
(3, 3, 4, '2015-01-15', 'Active', '2025-07-09 12:18:06'),
(4, 4, 4, '2016-03-01', 'Active', '2025-07-09 12:18:06'),
(5, 5, 4, '2017-05-15', 'Active', '2025-07-09 12:18:06'),
(6, 6, 4, '2018-02-01', 'Active', '2025-07-09 12:18:06'),
(7, 7, 4, '2019-01-10', 'Active', '2025-07-09 12:18:06'),
(8, 21, 4, '2016-02-01', 'Active', '2025-07-09 12:18:06'),
(9, 24, 4, '2015-03-01', 'Active', '2025-07-09 12:18:06'),
(10, 25, 4, '2016-07-01', 'Active', '2025-07-09 12:18:06'),
(11, 28, 4, '2014-08-01', 'Active', '2025-07-09 12:18:06'),
(12, 29, 4, '2015-12-01', 'Active', '2025-07-09 12:18:06'),
(13, 8, 5, '2018-06-01', 'Active', '2025-07-09 12:18:06'),
(14, 9, 5, '2019-03-15', 'Active', '2025-07-09 12:18:06'),
(15, 10, 5, '2020-01-20', 'Active', '2025-07-09 12:18:06'),
(16, 11, 5, '2020-05-10', 'Active', '2025-07-09 12:18:06'),
(17, 22, 5, '2017-08-15', 'Active', '2025-07-09 12:18:06'),
(18, 26, 5, '2017-11-01', 'Active', '2025-07-09 12:18:06'),
(19, 30, 5, '2016-04-01', 'Active', '2025-07-09 12:18:06'),
(20, 12, 6, '2019-08-01', 'Active', '2025-07-09 12:18:06'),
(21, 13, 6, '2020-11-15', 'Active', '2025-07-09 12:18:06'),
(22, 23, 6, '2018-05-01', 'Active', '2025-07-09 12:18:06'),
(23, 14, 7, '2017-07-01', 'Active', '2025-07-09 12:18:06'),
(24, 15, 7, '2018-09-01', 'Active', '2025-07-09 12:18:06'),
(25, 27, 7, '2018-01-15', 'Active', '2025-07-09 12:18:06'),
(26, 16, 8, '2019-04-01', 'Active', '2025-07-09 12:18:06'),
(27, 17, 8, '2020-02-15', 'Active', '2025-07-09 12:18:06'),
(28, 31, 8, '2017-06-15', 'Active', '2025-07-09 12:18:06'),
(29, 18, 9, '2016-06-01', 'Active', '2025-07-09 12:18:06'),
(30, 19, 9, '2018-10-01', 'Active', '2025-07-09 12:18:06'),
(31, 20, 10, '2014-12-01', 'Active', '2025-07-09 12:18:06');

-- --------------------------------------------------------

--
-- Table structure for table `visit_comments`
--

CREATE TABLE `visit_comments` (
  `comment_id` int(11) NOT NULL,
  `visit_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `comment_text` text NOT NULL,
  `comment_type` enum('Clinical','Administrative','Billing','Other') DEFAULT 'Clinical',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure for view `daily_revenue_summary`
--
DROP TABLE IF EXISTS `daily_revenue_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `daily_revenue_summary`  AS SELECT cast(`pb`.`bill_date` as date) AS `bill_date`, `pb`.`branch_id` AS `branch_id`, `b`.`branch_name` AS `branch_name`, count(`pb`.`bill_id`) AS `total_bills`, sum(`pb`.`total_amount`) AS `total_revenue`, sum(`pb`.`paid_amount`) AS `total_paid`, sum(`pb`.`balance_amount`) AS `total_outstanding`, count(distinct `pb`.`patient_id`) AS `unique_patients` FROM (`patient_bills` `pb` join `branches` `b` on(`pb`.`branch_id` = `b`.`branch_id`)) GROUP BY cast(`pb`.`bill_date` as date), `pb`.`branch_id` ;

-- --------------------------------------------------------

--
-- Structure for view `patient_summary`
--
DROP TABLE IF EXISTS `patient_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `patient_summary`  AS SELECT `p`.`patient_id` AS `patient_id`, `p`.`patient_number` AS `patient_number`, concat(`p`.`first_name`,' ',`p`.`last_name`) AS `patient_name`, `p`.`date_of_birth` AS `date_of_birth`, `p`.`gender` AS `gender`, `p`.`phone` AS `phone`, `p`.`email` AS `email`, count(distinct `pv`.`visit_id`) AS `total_visits`, count(distinct `pb`.`bill_id`) AS `total_bills`, coalesce(sum(`pb`.`total_amount`),0) AS `total_billed_amount`, coalesce(sum(`pb`.`paid_amount`),0) AS `total_paid_amount`, coalesce(sum(`pb`.`balance_amount`),0) AS `total_balance_amount`, `p`.`branch_id` AS `branch_id`, `b`.`branch_name` AS `branch_name` FROM (((`patients` `p` left join `patient_visits` `pv` on(`p`.`patient_id` = `pv`.`patient_id`)) left join `patient_bills` `pb` on(`p`.`patient_id` = `pb`.`patient_id`)) left join `branches` `b` on(`p`.`branch_id` = `b`.`branch_id`)) GROUP BY `p`.`patient_id` ;

-- --------------------------------------------------------

--
-- Structure for view `pharmacy_stock_status`
--
DROP TABLE IF EXISTS `pharmacy_stock_status`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `pharmacy_stock_status`  AS SELECT `pi`.`item_id` AS `item_id`, `pi`.`item_name` AS `item_name`, `pi`.`item_code` AS `item_code`, `pi`.`category` AS `category`, `pi`.`minimum_stock_level` AS `minimum_stock_level`, `pi`.`reorder_level` AS `reorder_level`, `inv`.`branch_id` AS `branch_id`, `b`.`branch_name` AS `branch_name`, sum(`inv`.`quantity_in_stock`) AS `current_stock`, CASE WHEN sum(`inv`.`quantity_in_stock`) <= `pi`.`minimum_stock_level` THEN 'Low Stock' WHEN sum(`inv`.`quantity_in_stock`) <= `pi`.`reorder_level` THEN 'Reorder' ELSE 'Normal' END AS `stock_status` FROM ((`pharmacy_items` `pi` join `pharmacy_inventory` `inv` on(`pi`.`item_id` = `inv`.`item_id`)) join `branches` `b` on(`inv`.`branch_id` = `b`.`branch_id`)) WHERE `pi`.`status` = 'Active' GROUP BY `pi`.`item_id`, `inv`.`branch_id` ;

-- --------------------------------------------------------

--
-- Structure for view `service_availability_coverage`
--
DROP TABLE IF EXISTS `service_availability_coverage`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `service_availability_coverage`  AS SELECT `bs`.`branch_service_id` AS `branch_service_id`, `bs`.`branch_id` AS `branch_id`, `b`.`branch_name` AS `branch_name`, `ms`.`service_id` AS `service_id`, `ms`.`service_name` AS `service_name`, `ms`.`service_code` AS `service_code`, `ms`.`service_category` AS `service_category`, `d`.`department_name` AS `department_name`, `bs`.`price` AS `price`, `bs`.`discount_percentage` AS `discount_percentage`, `bs`.`tax_percentage` AS `tax_percentage`, `bs`.`is_available` AS `is_available`, `bs`.`is_blocked` AS `is_blocked`, `bs`.`blocked_reason` AS `blocked_reason`, concat(`s`.`first_name`,' ',`s`.`last_name`) AS `blocked_by_name`, `bs`.`blocked_date` AS `blocked_date`, `bs`.`is_covered_by_scheme` AS `is_covered_by_scheme`, `bs`.`scheme_coverage_percentage` AS `scheme_coverage_percentage`, `bs`.`max_scheme_coverage_amount` AS `max_scheme_coverage_amount`, count(`ssc`.`coverage_id`) AS `scheme_specific_coverages` FROM (((((`branch_services` `bs` join `branches` `b` on(`bs`.`branch_id` = `b`.`branch_id`)) join `master_services` `ms` on(`bs`.`service_id` = `ms`.`service_id`)) join `departments` `d` on(`bs`.`department_id` = `d`.`department_id`)) left join `staff` `s` on(`bs`.`blocked_by` = `s`.`staff_id`)) left join `scheme_service_coverage` `ssc` on(`bs`.`service_id` = `ssc`.`service_id` and `bs`.`branch_id` = `ssc`.`branch_id` and `ssc`.`status` = 'Active')) GROUP BY `bs`.`branch_service_id` ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `banks`
--
ALTER TABLE `banks`
  ADD PRIMARY KEY (`bank_id`),
  ADD UNIQUE KEY `bank_code` (`bank_code`);

--
-- Indexes for table `bill_items`
--
ALTER TABLE `bill_items`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `bill_id` (`bill_id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `department_id` (`department_id`);

--
-- Indexes for table `branches`
--
ALTER TABLE `branches`
  ADD PRIMARY KEY (`branch_id`),
  ADD UNIQUE KEY `branch_code` (`branch_code`),
  ADD KEY `hospital_id` (`hospital_id`);

--
-- Indexes for table `branch_services`
--
ALTER TABLE `branch_services`
  ADD PRIMARY KEY (`branch_service_id`),
  ADD UNIQUE KEY `unique_branch_service` (`branch_id`,`service_id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `department_id` (`department_id`),
  ADD KEY `blocked_by` (`blocked_by`),
  ADD KEY `idx_branch_services_blocked` (`is_blocked`),
  ADD KEY `idx_branch_services_scheme_coverage` (`is_covered_by_scheme`);

--
-- Indexes for table `cashpoints`
--
ALTER TABLE `cashpoints`
  ADD PRIMARY KEY (`cashpoint_id`),
  ADD UNIQUE KEY `cashpoint_code` (`cashpoint_code`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `cashpoint_allocations`
--
ALTER TABLE `cashpoint_allocations`
  ADD PRIMARY KEY (`allocation_id`),
  ADD KEY `cashpoint_id` (`cashpoint_id`),
  ADD KEY `staff_id` (`staff_id`);

--
-- Indexes for table `clinics`
--
ALTER TABLE `clinics`
  ADD PRIMARY KEY (`clinic_id`),
  ADD UNIQUE KEY `clinic_code` (`clinic_code`),
  ADD KEY `department_id` (`department_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `credit_slips`
--
ALTER TABLE `credit_slips`
  ADD PRIMARY KEY (`slip_id`),
  ADD UNIQUE KEY `slip_number` (`slip_number`),
  ADD KEY `patient_id` (`patient_id`),
  ADD KEY `scheme_id` (`scheme_id`),
  ADD KEY `approved_by` (`approved_by`);

--
-- Indexes for table `departments`
--
ALTER TABLE `departments`
  ADD PRIMARY KEY (`department_id`),
  ADD UNIQUE KEY `department_code` (`department_code`),
  ADD KEY `head_of_department` (`head_of_department`);

--
-- Indexes for table `document_settings`
--
ALTER TABLE `document_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `unique_branch_document` (`branch_id`,`document_type`);

--
-- Indexes for table `hospitals`
--
ALTER TABLE `hospitals`
  ADD PRIMARY KEY (`hospital_id`),
  ADD UNIQUE KEY `registration_number` (`registration_number`);

--
-- Indexes for table `master_services`
--
ALTER TABLE `master_services`
  ADD PRIMARY KEY (`service_id`),
  ADD UNIQUE KEY `service_code` (`service_code`);

--
-- Indexes for table `nok_relationships`
--
ALTER TABLE `nok_relationships`
  ADD PRIMARY KEY (`relationship_id`),
  ADD UNIQUE KEY `relationship_name` (`relationship_name`);

--
-- Indexes for table `patients`
--
ALTER TABLE `patients`
  ADD PRIMARY KEY (`patient_id`),
  ADD UNIQUE KEY `patient_number` (`patient_number`),
  ADD KEY `emergency_contact_relationship_id` (`emergency_contact_relationship_id`),
  ADD KEY `idx_patients_patient_number` (`patient_number`),
  ADD KEY `idx_patients_phone` (`phone`),
  ADD KEY `idx_patients_email` (`email`),
  ADD KEY `idx_patients_branch_id` (`branch_id`);

--
-- Indexes for table `patient_bills`
--
ALTER TABLE `patient_bills`
  ADD PRIMARY KEY (`bill_id`),
  ADD UNIQUE KEY `bill_number` (`bill_number`),
  ADD KEY `cashpoint_id` (`cashpoint_id`),
  ADD KEY `billed_by` (`billed_by`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `idx_bills_patient_id` (`patient_id`),
  ADD KEY `idx_bills_visit_id` (`visit_id`),
  ADD KEY `idx_bills_bill_date` (`bill_date`),
  ADD KEY `idx_bills_bill_number` (`bill_number`);

--
-- Indexes for table `patient_limits`
--
ALTER TABLE `patient_limits`
  ADD PRIMARY KEY (`limit_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `patient_nok`
--
ALTER TABLE `patient_nok`
  ADD PRIMARY KEY (`nok_id`),
  ADD KEY `patient_id` (`patient_id`),
  ADD KEY `relationship_id` (`relationship_id`);

--
-- Indexes for table `patient_visits`
--
ALTER TABLE `patient_visits`
  ADD PRIMARY KEY (`visit_id`),
  ADD UNIQUE KEY `visit_number` (`visit_number`),
  ADD KEY `clinic_id` (`clinic_id`),
  ADD KEY `attending_doctor_id` (`attending_doctor_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `idx_visits_patient_id` (`patient_id`),
  ADD KEY `idx_visits_visit_date` (`visit_date`),
  ADD KEY `idx_visits_visit_number` (`visit_number`),
  ADD KEY `idx_visits_department_id` (`department_id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`payment_id`),
  ADD UNIQUE KEY `payment_number` (`payment_number`),
  ADD KEY `bill_id` (`bill_id`),
  ADD KEY `payment_mode_id` (`payment_mode_id`),
  ADD KEY `cashpoint_id` (`cashpoint_id`),
  ADD KEY `received_by` (`received_by`);

--
-- Indexes for table `payment_modes`
--
ALTER TABLE `payment_modes`
  ADD PRIMARY KEY (`payment_mode_id`),
  ADD UNIQUE KEY `mode_name` (`mode_name`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `permission_name` (`permission_name`);

--
-- Indexes for table `pharmacy_inventory`
--
ALTER TABLE `pharmacy_inventory`
  ADD PRIMARY KEY (`inventory_id`),
  ADD KEY `idx_pharmacy_inventory_item_id` (`item_id`),
  ADD KEY `idx_pharmacy_inventory_branch_id` (`branch_id`);

--
-- Indexes for table `pharmacy_items`
--
ALTER TABLE `pharmacy_items`
  ADD PRIMARY KEY (`item_id`),
  ADD UNIQUE KEY `item_code` (`item_code`);

--
-- Indexes for table `pharmacy_orders`
--
ALTER TABLE `pharmacy_orders`
  ADD PRIMARY KEY (`order_id`),
  ADD UNIQUE KEY `order_number` (`order_number`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `ordered_by` (`ordered_by`);

--
-- Indexes for table `pharmacy_order_items`
--
ALTER TABLE `pharmacy_order_items`
  ADD PRIMARY KEY (`order_item_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `item_id` (`item_id`);

--
-- Indexes for table `pharmacy_sales`
--
ALTER TABLE `pharmacy_sales`
  ADD PRIMARY KEY (`sale_id`),
  ADD UNIQUE KEY `sale_number` (`sale_number`),
  ADD KEY `payment_mode_id` (`payment_mode_id`),
  ADD KEY `cashpoint_id` (`cashpoint_id`),
  ADD KEY `sold_by` (`sold_by`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `idx_pharmacy_sales_patient_id` (`patient_id`),
  ADD KEY `idx_pharmacy_sales_date` (`sale_date`);

--
-- Indexes for table `pharmacy_sale_items`
--
ALTER TABLE `pharmacy_sale_items`
  ADD PRIMARY KEY (`sale_item_id`),
  ADD KEY `sale_id` (`sale_id`),
  ADD KEY `item_id` (`item_id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`role_id`),
  ADD UNIQUE KEY `role_name` (`role_name`);

--
-- Indexes for table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD PRIMARY KEY (`role_permission_id`),
  ADD UNIQUE KEY `unique_role_permission` (`role_id`,`permission_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- Indexes for table `schemes`
--
ALTER TABLE `schemes`
  ADD PRIMARY KEY (`scheme_id`),
  ADD UNIQUE KEY `scheme_code` (`scheme_code`),
  ADD KEY `provider_id` (`provider_id`);

--
-- Indexes for table `scheme_providers`
--
ALTER TABLE `scheme_providers`
  ADD PRIMARY KEY (`provider_id`),
  ADD UNIQUE KEY `provider_code` (`provider_code`);

--
-- Indexes for table `scheme_service_coverage`
--
ALTER TABLE `scheme_service_coverage`
  ADD PRIMARY KEY (`coverage_id`),
  ADD UNIQUE KEY `unique_scheme_service_branch` (`scheme_id`,`service_id`,`branch_id`),
  ADD KEY `idx_scheme_service_coverage_scheme` (`scheme_id`),
  ADD KEY `idx_scheme_service_coverage_service` (`service_id`),
  ADD KEY `idx_scheme_service_coverage_branch` (`branch_id`);

--
-- Indexes for table `staff`
--
ALTER TABLE `staff`
  ADD PRIMARY KEY (`staff_id`),
  ADD UNIQUE KEY `employee_id` (`employee_id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD KEY `idx_staff_employee_id` (`employee_id`),
  ADD KEY `idx_staff_branch_id` (`branch_id`),
  ADD KEY `idx_staff_department_id` (`department_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `idx_users_username` (`username`),
  ADD KEY `idx_users_email` (`email`),
  ADD KEY `idx_users_user_type` (`user_type`);

--
-- Indexes for table `user_roles`
--
ALTER TABLE `user_roles`
  ADD PRIMARY KEY (`user_role_id`),
  ADD UNIQUE KEY `unique_user_role` (`user_id`,`role_id`),
  ADD KEY `role_id` (`role_id`);

--
-- Indexes for table `visit_comments`
--
ALTER TABLE `visit_comments`
  ADD PRIMARY KEY (`comment_id`),
  ADD KEY `visit_id` (`visit_id`),
  ADD KEY `staff_id` (`staff_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `banks`
--
ALTER TABLE `banks`
  MODIFY `bank_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bill_items`
--
ALTER TABLE `bill_items`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `branches`
--
ALTER TABLE `branches`
  MODIFY `branch_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `branch_services`
--
ALTER TABLE `branch_services`
  MODIFY `branch_service_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cashpoints`
--
ALTER TABLE `cashpoints`
  MODIFY `cashpoint_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cashpoint_allocations`
--
ALTER TABLE `cashpoint_allocations`
  MODIFY `allocation_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `clinics`
--
ALTER TABLE `clinics`
  MODIFY `clinic_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `credit_slips`
--
ALTER TABLE `credit_slips`
  MODIFY `slip_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `departments`
--
ALTER TABLE `departments`
  MODIFY `department_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `document_settings`
--
ALTER TABLE `document_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `hospitals`
--
ALTER TABLE `hospitals`
  MODIFY `hospital_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `master_services`
--
ALTER TABLE `master_services`
  MODIFY `service_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `nok_relationships`
--
ALTER TABLE `nok_relationships`
  MODIFY `relationship_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `patients`
--
ALTER TABLE `patients`
  MODIFY `patient_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `patient_bills`
--
ALTER TABLE `patient_bills`
  MODIFY `bill_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `patient_limits`
--
ALTER TABLE `patient_limits`
  MODIFY `limit_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `patient_nok`
--
ALTER TABLE `patient_nok`
  MODIFY `nok_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `patient_visits`
--
ALTER TABLE `patient_visits`
  MODIFY `visit_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_modes`
--
ALTER TABLE `payment_modes`
  MODIFY `payment_mode_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `pharmacy_inventory`
--
ALTER TABLE `pharmacy_inventory`
  MODIFY `inventory_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pharmacy_items`
--
ALTER TABLE `pharmacy_items`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pharmacy_orders`
--
ALTER TABLE `pharmacy_orders`
  MODIFY `order_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pharmacy_order_items`
--
ALTER TABLE `pharmacy_order_items`
  MODIFY `order_item_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pharmacy_sales`
--
ALTER TABLE `pharmacy_sales`
  MODIFY `sale_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pharmacy_sale_items`
--
ALTER TABLE `pharmacy_sale_items`
  MODIFY `sale_item_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `role_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `role_permissions`
--
ALTER TABLE `role_permissions`
  MODIFY `role_permission_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=65;

--
-- AUTO_INCREMENT for table `schemes`
--
ALTER TABLE `schemes`
  MODIFY `scheme_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `scheme_providers`
--
ALTER TABLE `scheme_providers`
  MODIFY `provider_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `scheme_service_coverage`
--
ALTER TABLE `scheme_service_coverage`
  MODIFY `coverage_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `staff`
--
ALTER TABLE `staff`
  MODIFY `staff_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `user_roles`
--
ALTER TABLE `user_roles`
  MODIFY `user_role_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `visit_comments`
--
ALTER TABLE `visit_comments`
  MODIFY `comment_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `bill_items`
--
ALTER TABLE `bill_items`
  ADD CONSTRAINT `bill_items_ibfk_1` FOREIGN KEY (`bill_id`) REFERENCES `patient_bills` (`bill_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bill_items_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `master_services` (`service_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bill_items_ibfk_3` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE SET NULL;

--
-- Constraints for table `branches`
--
ALTER TABLE `branches`
  ADD CONSTRAINT `branches_ibfk_1` FOREIGN KEY (`hospital_id`) REFERENCES `hospitals` (`hospital_id`) ON DELETE CASCADE;

--
-- Constraints for table `branch_services`
--
ALTER TABLE `branch_services`
  ADD CONSTRAINT `branch_services_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `branch_services_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `master_services` (`service_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `branch_services_ibfk_3` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `branch_services_ibfk_4` FOREIGN KEY (`blocked_by`) REFERENCES `staff` (`staff_id`) ON DELETE SET NULL;

--
-- Constraints for table `cashpoints`
--
ALTER TABLE `cashpoints`
  ADD CONSTRAINT `cashpoints_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `cashpoint_allocations`
--
ALTER TABLE `cashpoint_allocations`
  ADD CONSTRAINT `cashpoint_allocations_ibfk_1` FOREIGN KEY (`cashpoint_id`) REFERENCES `cashpoints` (`cashpoint_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cashpoint_allocations_ibfk_2` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`staff_id`) ON DELETE CASCADE;

--
-- Constraints for table `clinics`
--
ALTER TABLE `clinics`
  ADD CONSTRAINT `clinics_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `clinics_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `credit_slips`
--
ALTER TABLE `credit_slips`
  ADD CONSTRAINT `credit_slips_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`patient_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `credit_slips_ibfk_2` FOREIGN KEY (`scheme_id`) REFERENCES `schemes` (`scheme_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `credit_slips_ibfk_3` FOREIGN KEY (`approved_by`) REFERENCES `staff` (`staff_id`) ON DELETE SET NULL;

--
-- Constraints for table `departments`
--
ALTER TABLE `departments`
  ADD CONSTRAINT `departments_ibfk_1` FOREIGN KEY (`head_of_department`) REFERENCES `staff` (`staff_id`) ON DELETE SET NULL;

--
-- Constraints for table `document_settings`
--
ALTER TABLE `document_settings`
  ADD CONSTRAINT `document_settings_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `patients`
--
ALTER TABLE `patients`
  ADD CONSTRAINT `patients_ibfk_1` FOREIGN KEY (`emergency_contact_relationship_id`) REFERENCES `nok_relationships` (`relationship_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `patients_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `patient_bills`
--
ALTER TABLE `patient_bills`
  ADD CONSTRAINT `patient_bills_ibfk_1` FOREIGN KEY (`visit_id`) REFERENCES `patient_visits` (`visit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `patient_bills_ibfk_2` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`patient_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `patient_bills_ibfk_3` FOREIGN KEY (`cashpoint_id`) REFERENCES `cashpoints` (`cashpoint_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `patient_bills_ibfk_4` FOREIGN KEY (`billed_by`) REFERENCES `staff` (`staff_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `patient_bills_ibfk_5` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `patient_limits`
--
ALTER TABLE `patient_limits`
  ADD CONSTRAINT `patient_limits_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `patient_nok`
--
ALTER TABLE `patient_nok`
  ADD CONSTRAINT `patient_nok_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`patient_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `patient_nok_ibfk_2` FOREIGN KEY (`relationship_id`) REFERENCES `nok_relationships` (`relationship_id`) ON DELETE CASCADE;

--
-- Constraints for table `patient_visits`
--
ALTER TABLE `patient_visits`
  ADD CONSTRAINT `patient_visits_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`patient_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `patient_visits_ibfk_2` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `patient_visits_ibfk_3` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`clinic_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `patient_visits_ibfk_4` FOREIGN KEY (`attending_doctor_id`) REFERENCES `staff` (`staff_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `patient_visits_ibfk_5` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`bill_id`) REFERENCES `patient_bills` (`bill_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`payment_mode_id`) REFERENCES `payment_modes` (`payment_mode_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_3` FOREIGN KEY (`cashpoint_id`) REFERENCES `cashpoints` (`cashpoint_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `payments_ibfk_4` FOREIGN KEY (`received_by`) REFERENCES `staff` (`staff_id`) ON DELETE CASCADE;

--
-- Constraints for table `pharmacy_inventory`
--
ALTER TABLE `pharmacy_inventory`
  ADD CONSTRAINT `pharmacy_inventory_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `pharmacy_items` (`item_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pharmacy_inventory_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `pharmacy_orders`
--
ALTER TABLE `pharmacy_orders`
  ADD CONSTRAINT `pharmacy_orders_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pharmacy_orders_ibfk_2` FOREIGN KEY (`ordered_by`) REFERENCES `staff` (`staff_id`) ON DELETE CASCADE;

--
-- Constraints for table `pharmacy_order_items`
--
ALTER TABLE `pharmacy_order_items`
  ADD CONSTRAINT `pharmacy_order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `pharmacy_orders` (`order_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pharmacy_order_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `pharmacy_items` (`item_id`) ON DELETE CASCADE;

--
-- Constraints for table `pharmacy_sales`
--
ALTER TABLE `pharmacy_sales`
  ADD CONSTRAINT `pharmacy_sales_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`patient_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `pharmacy_sales_ibfk_2` FOREIGN KEY (`payment_mode_id`) REFERENCES `payment_modes` (`payment_mode_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pharmacy_sales_ibfk_3` FOREIGN KEY (`cashpoint_id`) REFERENCES `cashpoints` (`cashpoint_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `pharmacy_sales_ibfk_4` FOREIGN KEY (`sold_by`) REFERENCES `staff` (`staff_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pharmacy_sales_ibfk_5` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `pharmacy_sale_items`
--
ALTER TABLE `pharmacy_sale_items`
  ADD CONSTRAINT `pharmacy_sale_items_ibfk_1` FOREIGN KEY (`sale_id`) REFERENCES `pharmacy_sales` (`sale_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pharmacy_sale_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `pharmacy_items` (`item_id`) ON DELETE CASCADE;

--
-- Constraints for table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE;

--
-- Constraints for table `schemes`
--
ALTER TABLE `schemes`
  ADD CONSTRAINT `schemes_ibfk_1` FOREIGN KEY (`provider_id`) REFERENCES `scheme_providers` (`provider_id`) ON DELETE CASCADE;

--
-- Constraints for table `scheme_service_coverage`
--
ALTER TABLE `scheme_service_coverage`
  ADD CONSTRAINT `scheme_service_coverage_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `schemes` (`scheme_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `scheme_service_coverage_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `master_services` (`service_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `scheme_service_coverage_ibfk_3` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE;

--
-- Constraints for table `staff`
--
ALTER TABLE `staff`
  ADD CONSTRAINT `staff_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_ibfk_3` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE SET NULL;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`branch_id`) ON DELETE SET NULL;

--
-- Constraints for table `user_roles`
--
ALTER TABLE `user_roles`
  ADD CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE CASCADE;

--
-- Constraints for table `visit_comments`
--
ALTER TABLE `visit_comments`
  ADD CONSTRAINT `visit_comments_ibfk_1` FOREIGN KEY (`visit_id`) REFERENCES `patient_visits` (`visit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `visit_comments_ibfk_2` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`staff_id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
